import { Hash, Tag } from "lucide-react";
import { toast } from "react-toastify";
import { useService } from "src/config/context/serviceProvider";
import { useAppForm } from "src/core/components/form/form";
import CloseModal from "src/core/components/CloseModal";
import { AppRuntime } from "src/core/service/utils/runtimes";
import { getErrorResult } from "src/core/utils/effectErrors";
import { cn } from "src/core/utils/classes";
import useCreateRecipe from "../../hooks/use-create-recipe";
import { CreateRecipeSchema } from "./schema";

interface Props {
	isOpen: boolean;
	setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
	recipeType: "bulk" | "unit";
}

export default function CreateRecipeModal({ isOpen, setIsOpen, recipeType }: Props) {
	const service = useService();
	const { recipe } = service;
	const { mutate } = useCreateRecipe();

	const form = useAppForm({
		defaultValues: {
			name: "",
			code: "",
			type: recipeType,
			productIDs: [],
			components: [],
		} as CreateRecipeSchema,
		validators: {
			onChange: CreateRecipeSchema,
		},
		onSubmit: ({ value }) => {
			mutate(value, {
				onError: (_error) => {
					console.log(_error);
					const { error } = getErrorResult(_error);
					toast.error(error.message);
				},
				onSettled: () => {
					handleClose();
					toast.success("Receta creada exitosamente");
				},
			});
		},
	});

	function handleClose() {
		form.reset();
		setIsOpen(false);
	}

	return (
		<div className={cn("modal", isOpen && "modal-open")}>
			<div className="modal-box">
				<CloseModal onClose={handleClose} />
				<h3 className="font-bold text-lg">
					Crear Receta {recipeType === 'bulk' ? 'a Granel' : 'por Unidad'}
				</h3>
				<form
					onSubmit={(e) => {
						e.preventDefault();
						e.stopPropagation();
						form.handleSubmit();
					}}
				>
					<form.AppForm>
						<fieldset className="fieldset">
							<form.AppField
								name="name"
								validators={{
									onChangeAsyncDebounceMs: 500,
									onChangeAsync: async ({ value }) => {
										if (!value || value.trim() === "") {
											return undefined;
										}
										try {
											await AppRuntime.runPromise(recipe.validateName(value));
											return undefined;
										} catch (e) {
											return [{ message: "El nombre ya existe" }];
										}
									},
								}}
								children={({ FSTextField }) => (
									<FSTextField
										label="Nombre"
										placeholder="Nombre de la receta"
										prefixComponent={<Tag size={16} />}
									/>
								)}
							/>
							<form.AppField
								name="code"
								validators={{
									onChangeAsyncDebounceMs: 500,
									onChangeAsync: async ({ value }) => {
										if (!value || value.trim() === "") {
											return undefined;
										}
										try {
											await AppRuntime.runPromise(recipe.validateCode(value));
											return undefined;
										} catch (e) {
											return [{ message: "El código ya existe" }];
										}
									},
								}}
								children={({ FSTextField }) => (
									<FSTextField
										label="Código"
										placeholder="Código de la receta"
										prefixComponent={<Hash size={16} />}
									/>
								)}
							/>
						</fieldset>
						<div className="modal-action">
							<form.SubscribeButton
								label="Crear Receta"
								className="btn btn-primary"
							/>
						</div>
					</form.AppForm>
				</form>
			</div>
		</div>
	);
}
