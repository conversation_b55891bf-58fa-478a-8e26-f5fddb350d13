import { Hash, Tag } from "lucide-react";
import { toast } from "react-toastify";
import { useService } from "src/config/context/serviceProvider";
import { useAppForm } from "src/core/components/form/form";
import CloseModal from "src/core/components/CloseModal";
import { AppRuntime } from "src/core/service/utils/runtimes";
import { getErrorResult } from "src/core/utils/effectErrors";
import { cn } from "src/core/utils/classes";
import useUpdateRecipe from "../../hooks/use-update-recipe";
import type { Recipe } from "../../service/model/recipe";
import { CreateRecipeSchema } from "../CreateRecipeModal/schema";

interface Props {
	isOpen: boolean;
	setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
	recipe: Recipe;
}

export default function EditRecipeForm({ isOpen, setIsOpen, recipe }: Props) {
	const service = useService();
	const { recipe: recipeService } = service;
	const { mutate } = useUpdateRecipe();

	const form = useAppForm({
		defaultValues: {
			name: recipe.name,
			code: recipe.code,
			type: recipe.type,
			productIDs: recipe.products.map(p => p.id),
			components: recipe.components.map(c => ({
				productID: c.product.id,
				quantity: c.quantity,
			})),
		} as CreateRecipeSchema,
		validators: {
			onChange: CreateRecipeSchema,
		},
		onSubmit: ({ value }) => {
			mutate(
				{
					id: recipe.id,
					...value,
				},
				{
					onError: (_error) => {
						console.log(_error);
						const { error } = getErrorResult(_error);
						toast.error(error.message);
					},
					onSettled: () => {
						handleClose();
						toast.success("Receta actualizada exitosamente");
					},
				},
			);
		},
	});

	function handleClose() {
		form.reset();
		setIsOpen(false);
	}

	return (
		<div className={cn("modal", isOpen && "modal-open")}>
			<div className="modal-box">
				<CloseModal onClose={handleClose} />
				<h3 className="font-bold text-lg">Editar Receta</h3>
				<form
					onSubmit={(e) => {
						e.preventDefault();
						e.stopPropagation();
						form.handleSubmit();
					}}
				>
					<form.AppForm>
						<fieldset className="fieldset">
							<form.AppField
								name="name"
								validators={{
									onChangeAsyncDebounceMs: 500,
									onChangeAsync: async ({ value }) => {
										if (!value || value.trim() === "" || value === recipe.name) {
											return undefined;
										}
										try {
											await AppRuntime.runPromise(
												recipeService.validateName(value),
											);
											return undefined;
										} catch (e) {
											return [{ message: "El nombre ya existe" }];
										}
									},
								}}
								children={({ FSTextField }) => (
									<FSTextField
										label="Nombre"
										placeholder="Nombre de la receta"
										prefixComponent={<Tag size={16} />}
									/>
								)}
							/>
							<form.AppField
								name="code"
								validators={{
									onChangeAsyncDebounceMs: 500,
									onChangeAsync: async ({ value }) => {
										if (!value || value.trim() === "" || value === recipe.code) {
											return undefined;
										}
										try {
											await AppRuntime.runPromise(
												recipeService.validateCode(value),
											);
											return undefined;
										} catch (e) {
											return [{ message: "El código ya existe" }];
										}
									},
								}}
								children={({ FSTextField }) => (
									<FSTextField
										label="Código"
										placeholder="Código de la receta"
										prefixComponent={<Hash size={16} />}
									/>
								)}
							/>
						</fieldset>
						<div className="modal-action">
							<form.SubscribeButton
								label="Actualizar Receta"
								className="btn btn-primary"
							/>
						</div>
					</form.AppForm>
				</form>
			</div>
		</div>
	);
}
